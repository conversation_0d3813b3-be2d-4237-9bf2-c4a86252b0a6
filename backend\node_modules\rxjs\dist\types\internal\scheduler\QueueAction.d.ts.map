{"version": 3, "file": "QueueAction.d.ts", "sourceRoot": "", "sources": ["../../../../src/internal/scheduler/QueueAction.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAC/C,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAClD,OAAO,EAAE,eAAe,EAAE,MAAM,UAAU,CAAC;AAC3C,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAE5C,qBAAa,WAAW,CAAC,CAAC,CAAE,SAAQ,WAAW,CAAC,CAAC,CAAC;IACpC,SAAS,CAAC,SAAS,EAAE,cAAc;IAAE,SAAS,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,IAAI;gBAAxF,SAAS,EAAE,cAAc,EAAY,IAAI,EAAE,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,IAAI;IAIvG,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,KAAK,GAAE,MAAU,GAAG,YAAY;IAUpD,OAAO,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,GAAG,GAAG;IAI5C,SAAS,CAAC,cAAc,CAAC,SAAS,EAAE,cAAc,EAAE,EAAE,CAAC,EAAE,WAAW,EAAE,KAAK,GAAE,MAAU,GAAG,WAAW;CAkBtG"}