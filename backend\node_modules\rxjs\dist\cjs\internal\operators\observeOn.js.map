{"version": 3, "file": "observeOn.js", "sourceRoot": "", "sources": ["../../../../src/internal/operators/observeOn.ts"], "names": [], "mappings": ";;;AAEA,2DAA0D;AAC1D,qCAAuC;AACvC,2DAAgE;AAsDhE,SAAgB,SAAS,CAAI,SAAwB,EAAE,KAAS;IAAT,sBAAA,EAAA,SAAS;IAC9D,OAAO,cAAO,CAAC,UAAC,MAAM,EAAE,UAAU;QAChC,MAAM,CAAC,SAAS,CACd,6CAAwB,CACtB,UAAU,EACV,UAAC,KAAK,IAAK,OAAA,iCAAe,CAAC,UAAU,EAAE,SAAS,EAAE,cAAM,OAAA,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,EAAtB,CAAsB,EAAE,KAAK,CAAC,EAA3E,CAA2E,EACtF,cAAM,OAAA,iCAAe,CAAC,UAAU,EAAE,SAAS,EAAE,cAAM,OAAA,UAAU,CAAC,QAAQ,EAAE,EAArB,CAAqB,EAAE,KAAK,CAAC,EAA1E,CAA0E,EAChF,UAAC,GAAG,IAAK,OAAA,iCAAe,CAAC,UAAU,EAAE,SAAS,EAAE,cAAM,OAAA,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,EAArB,CAAqB,EAAE,KAAK,CAAC,EAA1E,CAA0E,CACpF,CACF,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC;AAXD,8BAWC"}