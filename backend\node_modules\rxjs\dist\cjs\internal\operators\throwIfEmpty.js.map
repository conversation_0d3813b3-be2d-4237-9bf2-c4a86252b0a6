{"version": 3, "file": "throwIfEmpty.js", "sourceRoot": "", "sources": ["../../../../src/internal/operators/throwIfEmpty.ts"], "names": [], "mappings": ";;;AAAA,iDAAgD;AAEhD,qCAAuC;AACvC,2DAAgE;AAsChE,SAAgB,YAAY,CAAI,YAA6C;IAA7C,6BAAA,EAAA,kCAA6C;IAC3E,OAAO,cAAO,CAAC,UAAC,MAAM,EAAE,UAAU;QAChC,IAAI,QAAQ,GAAG,KAAK,CAAC;QACrB,MAAM,CAAC,SAAS,CACd,6CAAwB,CACtB,UAAU,EACV,UAAC,KAAK;YACJ,QAAQ,GAAG,IAAI,CAAC;YAChB,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzB,CAAC,EACD,cAAM,OAAA,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,EAArE,CAAqE,CAC5E,CACF,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC;AAdD,oCAcC;AAED,SAAS,mBAAmB;IAC1B,OAAO,IAAI,uBAAU,EAAE,CAAC;AAC1B,CAAC"}